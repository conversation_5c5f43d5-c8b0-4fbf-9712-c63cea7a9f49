name: Prepare for Environment Deployment

on:
  workflow_call:
    inputs:
      environment:
        description: 'Target environment (staging or prod)'
        required: true
        type: string
      img_tag:
        description: 'Docker image tag to deploy'
        required: true
        type: string
      build_sha:
        description: 'Git SHA to build from'
        required: true
        type: string
    outputs:
      deployment_completed:
        description: 'Whether deployment was completed successfully'
        value: ${{ jobs.create-devops-pr.outputs.deployment_completed }}

permissions:
  id-token: write
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ inputs.environment }}-${{ inputs.img_tag }}
  cancel-in-progress: false

jobs:
  build-and-push-image:
    uses: ./.github/workflows/build-and-push-image.yml
    with:
      ref: ${{ inputs.build_sha }}
      img_tag: ${{ inputs.img_tag }}
    secrets: inherit

  create-devops-pr:
    runs-on: ubuntu-latest
    needs: build-and-push-image
    outputs:
      deployment_completed: ${{ steps.deployment-status.outputs.completed }}
    steps:
      - name: Initialize Mandatory Git Config
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Clone Devops Repo
        run: |
          git clone https://${{secrets.PAT}}@github.com/rudderlabs/rudder-devops.git

      - name: Raise PR to update image in ${{ inputs.environment }}
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}
        run: |
          img_version=${{ inputs.img_tag }}
          environment=${{ inputs.environment }}
          cd rudder-devops
          branch_name="rudder-auth-$img_version"
          git checkout -b $branch_name

          # Determine environment path
          env_path="helm-charts/auth-service/environment/$environment"

          cd $env_path
          yq eval -i ".image=\"rudderstack/rudder-auth:$img_version\"" base.yaml
          git add base.yaml
          git commit -m "chore: upgrade rudder-auth to $img_version in $environment"
          git push -u origin $branch_name

          gh pr create --fill

      - name: Mark deployment as completed
        id: deployment-status
        run: |
          echo "completed=true" >> $GITHUB_OUTPUT
