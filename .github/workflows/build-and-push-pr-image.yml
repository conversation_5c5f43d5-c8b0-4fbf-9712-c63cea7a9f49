name: Build and Push PR Image
on:
  pull_request:
    branches:
      - develop

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build-and-push-image:
    # do not build for back merge pr from master to develop
    if: github.event.pull_request.head.ref != 'master'
    uses: ./.github/workflows/build-and-push-image.yml
    with:
      ref: ${{ github.event.pull_request.head.sha }}
      img_tag: ${{ github.head_ref }}
    secrets:
      inherit

      # We will not raise PR to devops for PRS
      # ----------------------------------------------------

      # - name: Initialize Mandatory Git Config
      #   run: |
      #     git config --global user.name "GitHub Actions"
      #     git config --global user.email "<EMAIL>"

      # - name: <PERSON><PERSON> Devops Repo
      #   run: |
      #     git clone https://${{secrets.PAT}}@github.com/rudderlabs/rudder-devops.git

      # - name: Raise PR to update image in staging
      #   env:
      #     GITHUB_TOKEN: ${{ secrets.PAT }}
      #   run: |
      #     img_version=${{ github.head_ref }}
      #     cd rudder-devops
      #     branch_name="rudder-auth-$img_version"
      #     git checkout -b $branch_name

      #     cd helm-charts/auth-service/environment/staging
      #     yq eval -i ".image=\"rudderstack/rudder-auth:$img_version\"" base.yaml
      #     git add base.yaml
      #     git commit -m "chore: upgrade staging rudder-auth to $img_version"
      #     git push -u origin $branch_name

      #     gh pr create --fill
