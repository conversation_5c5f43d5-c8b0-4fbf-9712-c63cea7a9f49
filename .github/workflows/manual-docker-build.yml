name: Manual Docker Build and Push

on:
  workflow_dispatch:
    inputs:
      branch_name:
        description: 'Branch name to build from (e.g., master, develop, release/1.2.1)'
        required: true
        type: string
      docker_image_name:
        description: 'Docker image name (e.g., rudderstack/rudder-auth)'
        required: true
        type: string
        default: 'rudderstack/rudder-auth'
      docker_tag:
        description: 'Docker tag (e.g., 1.2.1, staging-1.2.1, hotfix-1.2.2, manual-fix)'
        required: true
        type: string
      create_devops_pr:
        description: 'Create DevOps PR to update environment?'
        required: false
        type: boolean
        default: false
      target_environment:
        description: 'Target environment (only if creating DevOps PR)'
        required: false
        type: choice
        options:
          - staging
          - prod
        default: 'staging'

permissions:
  id-token: write
  contents: read

jobs:
  validate-inputs:
    name: Validate Inputs
    runs-on: ubuntu-latest
    outputs:
      branch_exists: ${{ steps.check-branch.outputs.exists }}
      full_image_tag: ${{ steps.generate-tag.outputs.full_tag }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0

      - name: Check if branch exists
        id: check-branch
        run: |
          branch_name="${{ github.event.inputs.branch_name }}"
          echo "Checking if branch '$branch_name' exists..."

          if git show-ref --verify --quiet refs/heads/$branch_name || git show-ref --verify --quiet refs/remotes/origin/$branch_name; then
            echo "✅ Branch '$branch_name' exists"
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Branch '$branch_name' does not exist"
            echo "Available branches:"
            git branch -a | head -10
            echo "exists=false" >> $GITHUB_OUTPUT
          fi

      - name: Generate full image tag
        id: generate-tag
        run: |
          image_name="${{ github.event.inputs.docker_image_name }}"
          tag="${{ github.event.inputs.docker_tag }}"
          full_tag="${image_name}:${tag}"

          echo "Full image tag: $full_tag"
          echo "full_tag=$full_tag" >> $GITHUB_OUTPUT

      - name: Validate inputs
        run: |
          if [[ "${{ steps.check-branch.outputs.exists }}" != "true" ]]; then
            echo "❌ Cannot proceed: Branch does not exist"
            exit 1
          fi

          # Validate image name format
          if [[ ! "${{ github.event.inputs.docker_image_name }}" =~ ^[a-z0-9]+([._-][a-z0-9]+)*(/[a-z0-9]+([._-][a-z0-9]+)*)*$ ]]; then
            echo "❌ Invalid Docker image name format"
            exit 1
          fi

          # Validate tag format
          if [[ ! "${{ github.event.inputs.docker_tag }}" =~ ^[a-zA-Z0-9._-]+$ ]]; then
            echo "❌ Invalid Docker tag format"
            exit 1
          fi

          echo "✅ All inputs validated successfully"

  get-branch-sha:
    name: Get Branch SHA
    runs-on: ubuntu-latest
    needs: validate-inputs
    if: needs.validate-inputs.outputs.branch_exists == 'true'
    outputs:
      build_sha: ${{ steps.get-sha.outputs.sha }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4.2.1
        with:
          fetch-depth: 0

      - name: Get SHA for branch
        id: get-sha
        run: |
          branch_name="${{ github.event.inputs.branch_name }}"

          # Try to get SHA from local branch first, then remote
          if git show-ref --verify --quiet refs/heads/$branch_name; then
            sha=$(git rev-parse refs/heads/$branch_name)
            echo "Using local branch SHA"
          elif git show-ref --verify --quiet refs/remotes/origin/$branch_name; then
            sha=$(git rev-parse refs/remotes/origin/$branch_name)
            echo "Using remote branch SHA"
          else
            echo "❌ Could not find SHA for branch $branch_name"
            exit 1
          fi

          echo "Branch: $branch_name"
          echo "SHA: $sha"
          echo "sha=$sha" >> $GITHUB_OUTPUT

  manual-build-and-push:
    name: Manual Build and Push
    needs: [validate-inputs, get-branch-sha]
    if: needs.validate-inputs.outputs.branch_exists == 'true'
    uses: ./.github/workflows/build-and-push-image.yml
    with:
      ref: ${{ needs.get-branch-sha.outputs.build_sha }}
      img_tag: ${{ github.event.inputs.docker_tag }}
    secrets: inherit

  create-devops-pr:
    name: Create DevOps PR (Optional)
    runs-on: ubuntu-latest
    needs: [validate-inputs, manual-build-and-push]
    if: github.event.inputs.create_devops_pr == 'true' && needs.manual-build-and-push.result == 'success'
    steps:
      - name: Initialize Mandatory Git Config
        run: |
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

      - name: Clone Devops Repo
        run: |
          git clone https://${{secrets.PAT}}@github.com/rudderlabs/rudder-devops.git

      - name: Create DevOps PR
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}
        run: |
          full_image_tag="${{ needs.validate-inputs.outputs.full_image_tag }}"
          environment="${{ github.event.inputs.target_environment }}"
          branch_name="${{ github.event.inputs.branch_name }}"

          cd rudder-devops

          # Create unique branch name with timestamp
          devops_branch="chore.upgrade-rudder-auth-to-${{ github.event.inputs.docker_tag }}"

          echo "Creating DevOps branch: $devops_branch"
          git checkout -b $devops_branch

          # Update environment
          env_path="helm-charts/auth-service/environment/$environment"

          if [[ ! -d "$env_path" ]]; then
            echo "❌ Environment path $env_path does not exist"
            exit 1
          fi

          cd $env_path

          echo "Updating $environment environment with image: $full_image_tag"
          yq eval -i ".image=\"$full_image_tag\"" base.yaml

          git add base.yaml
          git commit -m "chore: manual upgrade rudder-auth to $full_image_tag in $environment
          git push -u origin $devops_branch

          # Create PR
          gh pr create --fill

  summary:
    name: Build Summary
    runs-on: ubuntu-latest
    needs: [validate-inputs, get-branch-sha, manual-build-and-push, create-devops-pr]
    if: always()
    steps:
      - name: Print build summary
        run: |
          echo "## Manual Docker Build Summary"
          echo ""
          echo "**Inputs:**"
          echo "- Branch: ${{ github.event.inputs.branch_name }}"
          echo "- Image: ${{ github.event.inputs.docker_image_name }}:${{ github.event.inputs.docker_tag }}"
          echo "- Create DevOps PR: ${{ github.event.inputs.create_devops_pr }}"
          echo "- Target Environment: ${{ github.event.inputs.target_environment }}"
          echo ""
          echo "**Results:**"
          echo "- Input Validation: ${{ needs.validate-inputs.result }}"
          echo "- Get Branch SHA: ${{ needs.get-branch-sha.result }}"
          echo "- Docker Build: ${{ needs.manual-build-and-push.result }}"
          echo "- DevOps PR: ${{ needs.create-devops-pr.result || 'skipped' }}"
          echo ""

          if [[ "${{ needs.manual-build-and-push.result }}" == "success" ]]; then
            echo "✅ Docker image built and pushed successfully!"
            echo "Image: ${{ github.event.inputs.docker_image_name }}:${{ github.event.inputs.docker_tag }}"
          else
            echo "❌ Docker build failed. Check the logs for details."
          fi
