name: Create New Hotfix Branch

on:
  workflow_dispatch:
    inputs:
      hotfix_name:
        description: Hotfix branch name (e.g., temp -> hotfix/temp)
        required: true

jobs:
  create-branch:
    name: Create New Hotfix Branch
    runs-on: ubuntu-latest

    steps:
      # For cross team releases, we will avoid this check
      # - name: Check User Membership
      #   uses: tspascoal/get-user-teams-membership@v3
      #   id: checkUserMember
      #   with:
      #     username: ${{ github.actor }}
      #     team: 'integrations_team'
      #     GITHUB_TOKEN: ${{ secrets.PAT }}
      - name: Get Default Branch SHA
        id: get-sha
        run: |
          SHA=$(curl -s -H "Authorization: token ${{ secrets.PAT }}" \
            "https://api.github.com/repos/${{ github.repository }}/git/refs/heads/${{ github.event.repository.default_branch }}" \
            | jq -r '.object.sha')
          echo "sha=$SHA" >> $GITHUB_OUTPUT
          echo "Default branch SHA: $SHA"

      - name: Create Hotfix Branch
        run: |
          curl -s -X POST \
            -H "Authorization: token ${{ secrets.PAT }}" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${{ github.repository }}/git/refs" \
            -d '{
              "ref": "refs/heads/hotfix/${{ inputs.hotfix_name }}",
              "sha": "${{ steps.get-sha.outputs.sha }}"
            }'
          echo "Created hotfix branch: hotfix/${{ inputs.hotfix_name }}"
